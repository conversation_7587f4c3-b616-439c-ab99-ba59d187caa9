"""
OMR Grading Service - <PERSON><PERSON> thống chấm điểm tự động cho phiếu trắc nghiệm
Sử dụng OpenCV để phát hiện và xử lý bubble sheets
"""

import cv2
import numpy as np
import pandas as pd
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import io
from PIL import Image

logger = logging.getLogger(__name__)


class OMRGradingService:
    """Service xử lý chấm điểm tự động OMR"""

    def __init__(self):
        self.max_width = 1200
        self.bubble_threshold = 0.4  # 40% threshold cho bubble được tô
        self.min_contour_area = 50
        self.max_contour_area = 200

    def resize_image(self, image: np.ndarray) -> np.ndarray:
        """Resize ảnh về max width 1200px nếu cần"""
        height, width = image.shape[:2]
        if width > self.max_width:
            ratio = self.max_width / width
            new_height = int(height * ratio)
            image = cv2.resize(image, (self.max_width, new_height))
        return image

    def preprocess_image(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Bước 1-3: Tiền xử lý ảnh
        - Chuyển sang grayscale
        - GaussianBlur
        - Adaptive Threshold
        """
        # Chuyển sang grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # GaussianBlur để giảm noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Adaptive Threshold để tạo ảnh nhị phân
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )

        return gray, thresh

    def find_corner_squares(self, thresh: np.ndarray) -> List[np.ndarray]:
        """
        Bước 4: Phát hiện các contour hình vuông ở 4 góc
        """
        contours, _ = cv2.findContours(
            thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        corner_squares = []
        height, width = thresh.shape

        # Tìm các contour có diện tích lớn và hình vuông
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 1000:  # Diện tích tối thiểu cho corner square
                # Approximation để kiểm tra hình vuông
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) == 4:  # Hình tứ giác
                    # Kiểm tra tỷ lệ khung hình gần vuông
                    x, y, w, h = cv2.boundingRect(approx)
                    aspect_ratio = w / h
                    if 0.8 <= aspect_ratio <= 1.2:  # Gần vuông
                        corner_squares.append(approx)

        return corner_squares

    def order_points(self, pts: np.ndarray) -> np.ndarray:
        """Sắp xếp 4 điểm theo thứ tự: top-left, top-right, bottom-right, bottom-left"""
        rect = np.zeros((4, 2), dtype="float32")

        # Top-left có tổng x+y nhỏ nhất, bottom-right có tổng lớn nhất
        s = pts.sum(axis=1)
        rect[0] = pts[np.argmin(s)]
        rect[2] = pts[np.argmax(s)]

        # Top-right có diff x-y nhỏ nhất, bottom-left có diff lớn nhất
        diff = np.diff(pts, axis=1)
        rect[1] = pts[np.argmin(diff)]
        rect[3] = pts[np.argmax(diff)]

        return rect

    def perspective_transform(
        self, image: np.ndarray, corner_squares: List[np.ndarray]
    ) -> Optional[np.ndarray]:
        """
        Bước 5: Perspective transform để căn chỉnh ảnh
        """
        if len(corner_squares) < 4:
            logger.warning("Không tìm thấy đủ 4 góc để perspective transform")
            return None

        # Lấy 4 góc từ corner squares
        corners = []
        for square in corner_squares[:4]:
            # Lấy centroid của mỗi square
            M = cv2.moments(square)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                corners.append([cx, cy])

        if len(corners) != 4:
            return None

        # Sắp xếp các góc
        corners = np.array(corners, dtype="float32")
        rect = self.order_points(corners)

        # Tính kích thước ảnh đích
        (tl, tr, br, bl) = rect
        widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
        widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
        maxWidth = max(int(widthA), int(widthB))

        heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
        heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
        maxHeight = max(int(heightA), int(heightB))

        # Điểm đích
        dst = np.array(
            [
                [0, 0],
                [maxWidth - 1, 0],
                [maxWidth - 1, maxHeight - 1],
                [0, maxHeight - 1],
            ],
            dtype="float32",
        )

        # Perspective transform
        M = cv2.getPerspectiveTransform(rect, dst)
        warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight))

        return warped

    def extract_roi_regions(self, image: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Bước 6: Cắt các vùng ROI theo tỷ lệ phần trăm
        - Vùng số báo danh (góc phải trên)
        - Mã đề (góc phải trên)
        - Các đáp án (60 câu)
        """
        height, width = image.shape[:2]

        regions = {}

        # Vùng số báo danh (top-right corner)
        student_id_x1 = int(width * 0.7)
        student_id_y1 = int(height * 0.05)
        student_id_x2 = int(width * 0.95)
        student_id_y2 = int(height * 0.25)
        regions["student_id"] = image[
            student_id_y1:student_id_y2, student_id_x1:student_id_x2
        ]

        # Vùng mã đề (top-right, below student ID)
        test_code_x1 = int(width * 0.7)
        test_code_y1 = int(height * 0.25)
        test_code_x2 = int(width * 0.95)
        test_code_y2 = int(height * 0.4)
        regions["test_code"] = image[
            test_code_y1:test_code_y2, test_code_x1:test_code_x2
        ]

        # Vùng đáp án (60 câu) - chia thành 2 phần
        # Phần 1: câu 1-30 (bên trái)
        answers1_x1 = int(width * 0.1)
        answers1_y1 = int(height * 0.45)
        answers1_x2 = int(width * 0.5)
        answers1_y2 = int(height * 0.95)
        regions["answers_1_30"] = image[
            answers1_y1:answers1_y2, answers1_x1:answers1_x2
        ]

        # Phần 2: câu 31-60 (bên phải)
        answers2_x1 = int(width * 0.5)
        answers2_y1 = int(height * 0.45)
        answers2_x2 = int(width * 0.9)
        answers2_y2 = int(height * 0.95)
        regions["answers_31_60"] = image[
            answers2_y1:answers2_y2, answers2_x1:answers2_x2
        ]

        return regions

    def detect_bubbles_in_region(
        self, region: np.ndarray, grid_rows: int, grid_cols: int
    ) -> List[List[bool]]:
        """
        Bước 7-9: Phát hiện bubbles trong một vùng và sắp xếp thành lưới
        """
        # Preprocess region
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region.copy()

        # Threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Find contours
        contours, _ = cv2.findContours(
            thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        # Filter bubbles by area and circularity
        bubbles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_contour_area <= area <= self.max_contour_area:
                # Check circularity
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    if circularity > 0.3:  # Reasonably circular
                        # Get bounding box center
                        x, y, w, h = cv2.boundingRect(contour)
                        center_x = x + w // 2
                        center_y = y + h // 2
                        bubbles.append((center_x, center_y, contour))

        # Sort bubbles into grid
        height, width = region.shape[:2]
        cell_width = width // grid_cols
        cell_height = height // grid_rows

        grid = [[False for _ in range(grid_cols)] for _ in range(grid_rows)]

        for center_x, center_y, contour in bubbles:
            row = min(center_y // cell_height, grid_rows - 1)
            col = min(center_x // cell_width, grid_cols - 1)

            # Check if bubble is filled (black pixel ratio > threshold)
            mask = np.zeros(region.shape[:2], dtype=np.uint8)
            cv2.drawContours(mask, [contour], -1, 255, -1)

            # Count black pixels in original thresholded image
            masked_region = cv2.bitwise_and(thresh, mask)
            total_pixels = cv2.countNonZero(mask)
            black_pixels = cv2.countNonZero(masked_region)

            if total_pixels > 0:
                fill_ratio = black_pixels / total_pixels
                if fill_ratio > self.bubble_threshold:
                    grid[row][col] = True

        return grid

    def extract_student_id(self, student_id_region: np.ndarray) -> str:
        """Trích xuất số báo danh từ vùng ROI"""
        try:
            # Grid 8 hàng x 10 cột cho số báo danh (8 chữ số)
            grid = self.detect_bubbles_in_region(student_id_region, 8, 10)

            student_id = ""
            for row in range(8):
                for col in range(10):
                    if grid[row][col]:
                        student_id += str(col)
                        break
                else:
                    student_id += "0"  # Default nếu không tìm thấy

            return student_id if len(student_id) == 8 else "00000000"
        except:
            return "00000000"

    def extract_test_code(self, test_code_region: np.ndarray) -> str:
        """Trích xuất mã đề từ vùng ROI"""
        try:
            # Grid 4 hàng x 10 cột cho mã đề (4 chữ số)
            grid = self.detect_bubbles_in_region(test_code_region, 4, 10)

            test_code = ""
            for row in range(4):
                for col in range(10):
                    if grid[row][col]:
                        test_code += str(col)
                        break
                else:
                    test_code += "0"  # Default nếu không tìm thấy

            return test_code if len(test_code) == 4 else "0000"
        except:
            return "0000"

    def extract_answers(
        self, answers_region_1: np.ndarray, answers_region_2: np.ndarray
    ) -> Dict[int, str]:
        """Trích xuất đáp án từ 2 vùng (câu 1-30 và 31-60)"""
        answers = {}

        try:
            # Vùng 1: câu 1-30 (30 hàng x 4 cột A,B,C,D)
            grid1 = self.detect_bubbles_in_region(answers_region_1, 30, 4)
            for row in range(30):
                question_num = row + 1
                for col in range(4):
                    if grid1[row][col]:
                        answers[question_num] = chr(ord("A") + col)
                        break
                else:
                    answers[question_num] = None  # Không có đáp án

            # Vùng 2: câu 31-60 (30 hàng x 4 cột A,B,C,D)
            grid2 = self.detect_bubbles_in_region(answers_region_2, 30, 4)
            for row in range(30):
                question_num = row + 31
                for col in range(4):
                    if grid2[row][col]:
                        answers[question_num] = chr(ord("A") + col)
                        break
                else:
                    answers[question_num] = None  # Không có đáp án
        except Exception as e:
            logger.error(f"Error extracting answers: {e}")

        return answers

    def load_answer_keys_from_excel(self, excel_path: str) -> Dict[str, Dict[int, str]]:
        """Đọc đáp án từ file Excel"""
        try:
            df = pd.read_excel(excel_path)

            # Chuẩn hóa tên cột
            df.columns = [str(col).strip().replace("\ufeff", "") for col in df.columns]

            # Tìm cột mã đề (có thể là 'Mã Đề', 'TestCode', etc.)
            test_code_col = None
            for col in df.columns:
                if "mã" in col.lower() and "đề" in col.lower():
                    test_code_col = col
                    break
                elif "testcode" in col.lower():
                    test_code_col = col
                    break

            if not test_code_col:
                raise ValueError("Không tìm thấy cột mã đề trong file Excel")

            answer_keys = {}

            for _, row in df.iterrows():
                test_code = str(row[test_code_col]).strip()
                answers = {}

                # Tìm các cột câu hỏi
                for col in df.columns:
                    if (
                        col.startswith("question_")
                        or "câu hỏi" in col.lower()
                        or col.startswith("câu")
                    ):
                        try:
                            if col.startswith("question_"):
                                question_num = int(col.split("_")[1])
                            elif "câu hỏi" in col.lower():
                                # Xử lý format "Câu hỏi 1", "Câu hỏi 2", etc.
                                parts = col.split()
                                question_num = int(parts[-1])
                            else:
                                # Xử lý format "câu 1", "câu 2", etc.
                                question_num = int(col.split()[-1])

                            answer = str(row[col]).strip().upper()
                            if answer in ["A", "B", "C", "D"]:
                                answers[question_num] = answer
                        except (ValueError, IndexError):
                            continue

                answer_keys[test_code] = answers

            return answer_keys

        except Exception as e:
            logger.error(f"Error loading answer keys: {e}")
            return {}

    def grade_answers(
        self, student_answers: Dict[int, str], correct_answers: Dict[int, str]
    ) -> Dict[str, Any]:
        """Chấm điểm đáp án"""
        if not correct_answers:
            return {
                "total_questions": len(student_answers),
                "correct_count": 0,
                "score": 0.0,
                "percentage": 0.0,
                "details": [],
            }

        total_questions = len(correct_answers)
        correct_count = 0
        details = []

        for question_num in range(1, total_questions + 1):
            student_answer = student_answers.get(question_num)
            correct_answer = correct_answers.get(question_num)

            is_correct = student_answer == correct_answer
            if is_correct:
                correct_count += 1

            details.append(
                {
                    "question": question_num,
                    "student_answer": student_answer,
                    "correct_answer": correct_answer,
                    "is_correct": is_correct,
                }
            )

        score = (correct_count / total_questions) * 10 if total_questions > 0 else 0
        percentage = (
            (correct_count / total_questions) * 100 if total_questions > 0 else 0
        )

        return {
            "total_questions": total_questions,
            "correct_count": correct_count,
            "score": round(score, 2),
            "percentage": round(percentage, 2),
            "details": details,
        }

    def process_omr_sheet(
        self, image_path: str, excel_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Bước 10-11: Xử lý hoàn chỉnh một phiếu OMR
        """
        try:
            # Đọc ảnh
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Không thể đọc ảnh từ {image_path}")

            # Bước 1: Resize ảnh
            image = self.resize_image(image)

            # Bước 2-3: Tiền xử lý
            gray, thresh = self.preprocess_image(image)

            # Bước 4: Tìm corner squares
            corner_squares = self.find_corner_squares(thresh)

            # Bước 5: Perspective transform
            aligned_image = self.perspective_transform(image, corner_squares)
            if aligned_image is None:
                logger.warning(
                    "Không thể thực hiện perspective transform, sử dụng ảnh gốc"
                )
                aligned_image = image

            # Bước 6: Cắt các vùng ROI
            regions = self.extract_roi_regions(aligned_image)

            # Bước 7-9: Trích xuất thông tin
            student_id = self.extract_student_id(regions["student_id"])
            test_code = self.extract_test_code(regions["test_code"])
            answers = self.extract_answers(
                regions["answers_1_30"], regions["answers_31_60"]
            )

            # Tạo kết quả cơ bản
            result = {
                "success": True,
                "student_id": student_id,
                "test_code": test_code,
                "answers": answers,
                "processing_info": {
                    "image_path": image_path,
                    "perspective_corrected": aligned_image is not image,
                    "corner_squares_found": len(corner_squares),
                },
            }

            # Bước 11: Chấm điểm nếu có file Excel
            if excel_path and Path(excel_path).exists():
                answer_keys = self.load_answer_keys_from_excel(excel_path)
                if test_code in answer_keys:
                    grading_result = self.grade_answers(answers, answer_keys[test_code])
                    result["grading"] = grading_result
                    result["answer_key_found"] = True
                else:
                    result["grading"] = None
                    result["answer_key_found"] = False
                    result["available_test_codes"] = list(answer_keys.keys())
            else:
                result["grading"] = None
                result["answer_key_found"] = False

            return result

        except Exception as e:
            logger.error(f"Error processing OMR sheet: {e}")
            return {
                "success": False,
                "error": str(e),
                "student_id": "00000000",
                "test_code": "0000",
                "answers": {},
                "grading": None,
            }

    def create_result_visualization(
        self, image_path: str, result: Dict[str, Any], output_path: str
    ):
        """Tạo ảnh kết quả với marking"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return

            # Vẽ thông tin lên ảnh
            font = cv2.FONT_HERSHEY_SIMPLEX

            # Thông tin sinh viên
            cv2.putText(
                image,
                f"Student ID: {result['student_id']}",
                (10, 30),
                font,
                0.7,
                (0, 255, 0),
                2,
            )
            cv2.putText(
                image,
                f"Test Code: {result['test_code']}",
                (10, 60),
                font,
                0.7,
                (0, 255, 0),
                2,
            )

            # Thông tin chấm điểm
            if result.get("grading"):
                grading = result["grading"]
                cv2.putText(
                    image,
                    f"Score: {grading['score']}/10 ({grading['percentage']}%)",
                    (10, 90),
                    font,
                    0.7,
                    (0, 0, 255),
                    2,
                )
                cv2.putText(
                    image,
                    f"Correct: {grading['correct_count']}/{grading['total_questions']}",
                    (10, 120),
                    font,
                    0.7,
                    (0, 0, 255),
                    2,
                )

            # Lưu ảnh kết quả
            cv2.imwrite(output_path, image)
            logger.info(f"Result visualization saved to {output_path}")

        except Exception as e:
            logger.error(f"Error creating result visualization: {e}")


# Tạo instance global
omr_grading_service = OMRGradingService()

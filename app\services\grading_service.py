"""
Grading Service - Wrapper cho OMR grading service
"""

from typing import List, Dict, Any
from fastapi import UploadFile
import tempfile
import os
from pathlib import Path

from app.services.omr_grading_service import omr_grading_service

async def batch_grade_all(image_files: List[UploadFile], excel_file: UploadFile) -> List[Dict[str, Any]]:
    """
    Chấm điểm hàng loạt cho nhiều ảnh bài làm
    
    Args:
        image_files: Danh sách file ảnh bài làm
        excel_file: File Excel chứa đáp án
        
    Returns:
        List kết quả chấm điểm cho từng ảnh
    """
    results = []
    
    # Tạo file tạm cho Excel
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_excel:
        excel_content = await excel_file.read()
        temp_excel.write(excel_content)
        temp_excel_path = temp_excel.name
    
    try:
        # X<PERSON> lý từng ảnh
        for i, image_file in enumerate(image_files):
            try:
                # Tạo file tạm cho ảnh
                with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_image:
                    image_content = await image_file.read()
                    temp_image.write(image_content)
                    temp_image_path = temp_image.name
                
                # Xử lý OMR
                result = omr_grading_service.process_omr_sheet(temp_image_path, temp_excel_path)
                result['image_filename'] = image_file.filename
                result['image_index'] = i + 1
                
                results.append(result)
                
                # Cleanup ảnh tạm
                os.unlink(temp_image_path)
                
            except Exception as e:
                results.append({
                    "success": False,
                    "error": str(e),
                    "image_filename": image_file.filename,
                    "image_index": i + 1,
                    "student_id": "00000000",
                    "test_code": "0000",
                    "answers": {},
                    "grading": None
                })
    
    finally:
        # Cleanup Excel tạm
        if Path(temp_excel_path).exists():
            os.unlink(temp_excel_path)
    
    return results

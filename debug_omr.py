"""
Debug script để phân tích chi tiết quá trình OMR
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import cv2
import numpy as np
from app.services.omr_grading_service import omr_grading_service
import pandas as pd

def debug_image_processing():
    """Debug chi tiết quá trình xử lý ảnh"""
    print("🔍 Debug OMR Image Processing")
    print("=" * 50)
    
    image_path = "data/grading/1.jpeg"
    
    # Đọc ảnh gốc
    image = cv2.imread(image_path)
    print(f"📷 Original image shape: {image.shape}")
    
    # Resize
    resized = omr_grading_service.resize_image(image)
    print(f"📏 Resized image shape: {resized.shape}")
    
    # Preprocessing
    gray, thresh = omr_grading_service.preprocess_image(resized)
    print(f"🔧 Preprocessed shapes - Gray: {gray.shape}, Thresh: {thresh.shape}")
    
    # Save intermediate images for inspection
    cv2.imwrite("debug_gray.jpg", gray)
    cv2.imwrite("debug_thresh.jpg", thresh)
    print("💾 Saved debug_gray.jpg and debug_thresh.jpg")
    
    # Corner detection
    corner_squares = omr_grading_service.find_corner_squares(thresh)
    print(f"🔲 Found {len(corner_squares)} corner squares")
    
    # Draw corner squares on image for visualization
    debug_corners = resized.copy()
    for i, square in enumerate(corner_squares):
        cv2.drawContours(debug_corners, [square], -1, (0, 255, 0), 3)
        # Add text label
        M = cv2.moments(square)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            cv2.putText(debug_corners, f"Corner {i+1}", (cx-30, cy), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    cv2.imwrite("debug_corners.jpg", debug_corners)
    print("💾 Saved debug_corners.jpg")
    
    # ROI extraction
    regions = omr_grading_service.extract_roi_regions(resized)
    print(f"✂️ Extracted {len(regions)} ROI regions:")
    
    for region_name, region in regions.items():
        print(f"   - {region_name}: {region.shape}")
        cv2.imwrite(f"debug_roi_{region_name}.jpg", region)
    
    print("💾 Saved all ROI debug images")
    
    # Test specific region processing
    print("\n🔍 Testing specific region processing:")
    
    # Test student ID extraction
    student_id_region = regions['student_id']
    student_id = omr_grading_service.extract_student_id(student_id_region)
    print(f"🆔 Student ID detected: {student_id}")
    
    # Test test code extraction
    test_code_region = regions['test_code']
    test_code = omr_grading_service.extract_test_code(test_code_region)
    print(f"📝 Test Code detected: {test_code}")
    
    # Test bubble detection in test code region
    print(f"\n🔍 Analyzing test code region in detail:")
    grid = omr_grading_service.detect_bubbles_in_region(test_code_region, 4, 10)
    print(f"Grid shape: {len(grid)} rows x {len(grid[0])} cols")
    
    for row in range(4):
        row_data = []
        for col in range(10):
            if grid[row][col]:
                row_data.append(f"[{col}]")
            else:
                row_data.append(" . ")
        print(f"Row {row}: {' '.join(row_data)}")
    
    return regions

def debug_excel_data():
    """Debug dữ liệu Excel"""
    print("\n📊 Debug Excel Data")
    print("=" * 30)
    
    excel_path = "data/grading/sample_answer_keys.xlsx"
    
    # Load raw Excel data
    df = pd.read_excel(excel_path)
    print(f"📋 Excel shape: {df.shape}")
    print(f"📋 Columns: {df.columns.tolist()}")
    
    # Show first few rows
    print(f"\n📋 First 5 rows:")
    print(df.head())
    
    # Load using OMR service
    answer_keys = omr_grading_service.load_answer_keys_from_excel(excel_path)
    print(f"\n🔑 Loaded answer keys for test codes: {list(answer_keys.keys())}")
    
    for test_code, answers in answer_keys.items():
        print(f"   - Test code '{test_code}': {len(answers)} answers")
        if answers:
            sample_answers = dict(list(answers.items())[:5])
            print(f"     Sample: {sample_answers}")

def suggest_improvements():
    """Đề xuất cải thiện"""
    print("\n💡 Suggestions for Improvement")
    print("=" * 40)
    
    print("1. 🔲 Corner Detection:")
    print("   - Only found 1 corner square instead of 4")
    print("   - Consider adjusting contour area thresholds")
    print("   - Try different approximation epsilon values")
    
    print("\n2. 📝 Test Code Detection:")
    print("   - Currently detecting '0000' instead of actual test code")
    print("   - May need to adjust ROI coordinates")
    print("   - Consider improving bubble detection threshold")
    
    print("\n3. 🎯 Bubble Detection:")
    print("   - Fine-tune fill ratio threshold (currently 40%)")
    print("   - Adjust contour area filters")
    print("   - Consider adaptive thresholding parameters")
    
    print("\n4. 📐 Perspective Correction:")
    print("   - Implement fallback methods when 4 corners not found")
    print("   - Consider using edge detection for sheet boundaries")
    
    print("\n5. 🔧 ROI Positioning:")
    print("   - Current ROI coordinates may need adjustment")
    print("   - Consider making ROI coordinates configurable")
    print("   - Test with different image resolutions")

def main():
    """Main debug function"""
    print("🐛 OMR Debug Analysis")
    print("=" * 60)
    
    # Debug image processing
    regions = debug_image_processing()
    
    # Debug Excel data
    debug_excel_data()
    
    # Suggestions
    suggest_improvements()
    
    print("\n" + "=" * 60)
    print("✅ Debug analysis complete!")
    print("📁 Check the generated debug images:")
    print("   - debug_gray.jpg (grayscale)")
    print("   - debug_thresh.jpg (threshold)")
    print("   - debug_corners.jpg (corner detection)")
    print("   - debug_roi_*.jpg (ROI regions)")

if __name__ == "__main__":
    main()

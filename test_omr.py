"""
Script test hệ thống OMR Grading
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.omr_grading_service import omr_grading_service
import json
from pathlib import Path

def test_omr_processing():
    """Test xử lý OMR với dữ liệu mẫu"""
    print("🔍 Testing OMR Processing System...")
    print("=" * 50)
    
    # Đường dẫn file test
    image_path = "data/grading/1.jpeg"
    excel_path = "data/grading/sample_answer_keys.xlsx"
    
    # Kiểm tra file tồn tại
    if not Path(image_path).exists():
        print(f"❌ Không tìm thấy file ảnh: {image_path}")
        return False
    
    if not Path(excel_path).exists():
        print(f"❌ Không tìm thấy file Excel: {excel_path}")
        return False
    
    print(f"📁 Image file: {image_path}")
    print(f"📁 Excel file: {excel_path}")
    print()
    
    try:
        # Test đọc đáp án từ Excel
        print("📖 Testing Excel answer key loading...")
        answer_keys = omr_grading_service.load_answer_keys_from_excel(excel_path)
        print(f"✅ Loaded answer keys for {len(answer_keys)} test codes")
        for test_code, answers in answer_keys.items():
            print(f"   - Test code {test_code}: {len(answers)} questions")
        print()
        
        # Test xử lý OMR hoàn chỉnh
        print("🔄 Processing OMR sheet...")
        result = omr_grading_service.process_omr_sheet(image_path, excel_path)
        
        print("📊 OMR Processing Results:")
        print("=" * 30)
        print(f"✅ Success: {result['success']}")
        print(f"🆔 Student ID: {result['student_id']}")
        print(f"📝 Test Code: {result['test_code']}")
        print(f"📋 Total Answers: {len(result['answers'])}")
        
        # Hiển thị một số đáp án mẫu
        print("\n📝 Sample Answers:")
        for i, (q_num, answer) in enumerate(list(result['answers'].items())[:10]):
            print(f"   Question {q_num}: {answer}")
        if len(result['answers']) > 10:
            print(f"   ... and {len(result['answers']) - 10} more")
        
        # Hiển thị kết quả chấm điểm
        if result.get('grading'):
            grading = result['grading']
            print(f"\n🎯 Grading Results:")
            print(f"   📊 Score: {grading['score']}/10 ({grading['percentage']}%)")
            print(f"   ✅ Correct: {grading['correct_count']}/{grading['total_questions']}")
            print(f"   🔍 Answer key found: {result['answer_key_found']}")
        else:
            print(f"\n❌ No grading performed")
            print(f"   🔍 Answer key found: {result.get('answer_key_found', False)}")
            if 'available_test_codes' in result:
                print(f"   📋 Available test codes: {result['available_test_codes']}")
        
        # Hiển thị thông tin xử lý
        if 'processing_info' in result:
            info = result['processing_info']
            print(f"\n🔧 Processing Info:")
            print(f"   📐 Perspective corrected: {info.get('perspective_corrected', False)}")
            print(f"   🔲 Corner squares found: {info.get('corner_squares_found', 0)}")
        
        # Tạo ảnh kết quả
        print(f"\n🖼️ Creating result visualization...")
        output_path = "data/grading/test_result.jpg"
        omr_grading_service.create_result_visualization(image_path, result, output_path)
        if Path(output_path).exists():
            print(f"✅ Result image saved to: {output_path}")
        else:
            print(f"❌ Failed to create result image")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during OMR processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """Test các component riêng lẻ"""
    print("\n🧪 Testing Individual Components...")
    print("=" * 50)
    
    image_path = "data/grading/1.jpeg"
    
    try:
        import cv2
        
        # Test đọc và resize ảnh
        print("📷 Testing image loading and resizing...")
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Cannot load image: {image_path}")
            return False
        
        print(f"   Original size: {image.shape[1]}x{image.shape[0]}")
        resized = omr_grading_service.resize_image(image)
        print(f"   Resized size: {resized.shape[1]}x{resized.shape[0]}")
        
        # Test preprocessing
        print("🔧 Testing image preprocessing...")
        gray, thresh = omr_grading_service.preprocess_image(resized)
        print(f"   Grayscale shape: {gray.shape}")
        print(f"   Threshold shape: {thresh.shape}")
        
        # Test corner detection
        print("🔍 Testing corner square detection...")
        corner_squares = omr_grading_service.find_corner_squares(thresh)
        print(f"   Found {len(corner_squares)} corner squares")
        
        # Test ROI extraction
        print("✂️ Testing ROI extraction...")
        regions = omr_grading_service.extract_roi_regions(resized)
        print(f"   Extracted {len(regions)} regions:")
        for region_name, region in regions.items():
            print(f"     - {region_name}: {region.shape}")
        
        print("✅ All individual components working!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing components: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 OMR Grading System Test")
    print("=" * 60)
    
    # Test individual components
    component_test = test_individual_components()
    
    # Test full processing
    full_test = test_omr_processing()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   🧪 Component Test: {'✅ PASSED' if component_test else '❌ FAILED'}")
    print(f"   🔄 Full Processing Test: {'✅ PASSED' if full_test else '❌ FAILED'}")
    
    if component_test and full_test:
        print("\n🎉 All tests PASSED! OMR system is working correctly.")
        print("\n📝 Next steps:")
        print("   1. Start the FastAPI server: fastapi dev app/main.py")
        print("   2. Test API endpoints at: http://localhost:8000/api/v1/docs")
        print("   3. Try the /omr/test-with-sample endpoint")
    else:
        print("\n❌ Some tests FAILED. Please check the errors above.")
    
    return component_test and full_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

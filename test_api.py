"""
Script test API OMR
"""

import requests
import json

def test_omr_api():
    """Test OMR API endpoints"""
    base_url = "http://localhost:8000/api/v1/omr"
    
    print("🚀 Testing OMR API Endpoints")
    print("=" * 50)
    
    # Test health endpoint
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        print("   ✅ Health check passed")
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
    
    print()
    
    # Test sample data endpoint
    print("2. Testing sample data processing...")
    try:
        response = requests.post(f"{base_url}/test-with-sample")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ Sample processing successful!")
            print(f"   📊 Results:")
            print(f"      - Success: {result.get('success')}")
            print(f"      - Student ID: {result.get('student_id')}")
            print(f"      - Test Code: {result.get('test_code')}")
            print(f"      - Total Answers: {len(result.get('answers', {}))}")
            
            if result.get('grading'):
                grading = result['grading']
                print(f"      - Score: {grading['score']}/10 ({grading['percentage']}%)")
                print(f"      - Correct: {grading['correct_count']}/{grading['total_questions']}")
            else:
                print(f"      - No grading performed")
                print(f"      - Answer key found: {result.get('answer_key_found')}")
        else:
            print(f"   ❌ Sample processing failed")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Sample processing error: {e}")
    
    print()
    
    # Test visualization endpoint
    print("3. Testing sample visualization...")
    try:
        response = requests.get(f"{base_url}/test-sample-visualization")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Visualization successful!")
            print(f"   📷 Image size: {len(response.content)} bytes")
            
            # Save image
            with open("test_visualization_result.jpg", "wb") as f:
                f.write(response.content)
            print("   💾 Saved as: test_visualization_result.jpg")
        else:
            print(f"   ❌ Visualization failed")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Visualization error: {e}")
    
    print()
    print("=" * 50)
    print("🎯 API Test Summary:")
    print("   - Health check: Available")
    print("   - Sample processing: Available") 
    print("   - Visualization: Available")
    print()
    print("📝 Next steps:")
    print("   1. Check the browser at: http://localhost:8000/api/v1/docs")
    print("   2. Try uploading your own images using the /process-sheet endpoint")
    print("   3. Use the /process-with-visualization endpoint for visual results")

if __name__ == "__main__":
    test_omr_api()

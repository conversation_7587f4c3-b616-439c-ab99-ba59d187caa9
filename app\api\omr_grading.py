"""
OMR Grading API - Endpoints cho chấm điểm tự động
"""

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse, FileResponse
import tempfile
import os
import logging
from pathlib import Path
from typing import Optional

from app.services.omr_grading_service import omr_grading_service

logger = logging.getLogger(__name__)

router = APIRouter(tags=["OMR Grading"])


@router.post("/process-sheet")
async def process_omr_sheet(
    image_file: UploadFile = File(..., description="Ảnh phiếu trắc nghiệm"),
    answer_key_file: Optional[UploadFile] = File(
        None, description="File Excel chứa đáp án (tùy chọn)"
    ),
):
    """
    Xử lý phiếu trắc nghiệm OMR

    - **image_file**: Ảnh phiếu trắc nghiệm (JPG, PNG)
    - **answer_key_file**: File Excel chứa đáp án (tùy chọn)

    Returns:
    - student_id: <PERSON><PERSON> báo danh
    - test_code: <PERSON>ã đề thi
    - answers: <PERSON><PERSON><PERSON> án đã chọn
    - grading: <PERSON><PERSON><PERSON> quả chấm điểm (nếu có đáp án)
    """
    try:
        # Kiểm tra file ảnh
        if not image_file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File phải là ảnh (JPG, PNG)")

        # Tạo file tạm cho ảnh
        with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_image:
            content = await image_file.read()
            temp_image.write(content)
            temp_image_path = temp_image.name

        # Tạo file tạm cho Excel nếu có
        temp_excel_path = None
        if answer_key_file:
            if not answer_key_file.filename.endswith((".xlsx", ".xls")):
                raise HTTPException(
                    status_code=400, detail="File đáp án phải là Excel (.xlsx, .xls)"
                )

            with tempfile.NamedTemporaryFile(
                delete=False, suffix=".xlsx"
            ) as temp_excel:
                excel_content = await answer_key_file.read()
                temp_excel.write(excel_content)
                temp_excel_path = temp_excel.name

        # Xử lý OMR
        result = omr_grading_service.process_omr_sheet(temp_image_path, temp_excel_path)

        # Cleanup
        os.unlink(temp_image_path)
        if temp_excel_path:
            os.unlink(temp_excel_path)

        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Error processing OMR sheet: {e}")
        # Cleanup on error
        try:
            if "temp_image_path" in locals():
                os.unlink(temp_image_path)
            if "temp_excel_path" in locals() and temp_excel_path:
                os.unlink(temp_excel_path)
        except:
            pass

        raise HTTPException(status_code=500, detail=f"Lỗi xử lý OMR: {str(e)}")


@router.post("/test-with-sample")
async def test_with_sample_data():
    """
    Test OMR với dữ liệu mẫu có sẵn
    Sử dụng ảnh 1.jpeg và sample_answer_keys.xlsx
    """
    try:
        # Đường dẫn file mẫu
        image_path = "data/grading/1.jpeg"
        excel_path = "data/grading/sample_answer_keys.xlsx"

        # Kiểm tra file tồn tại
        if not Path(image_path).exists():
            raise HTTPException(
                status_code=404, detail=f"Không tìm thấy file ảnh: {image_path}"
            )

        if not Path(excel_path).exists():
            raise HTTPException(
                status_code=404, detail=f"Không tìm thấy file Excel: {excel_path}"
            )

        # Xử lý OMR
        result = omr_grading_service.process_omr_sheet(image_path, excel_path)

        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Error testing OMR with sample data: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi test OMR: {str(e)}")


@router.post("/process-with-visualization")
async def process_with_visualization(
    image_file: UploadFile = File(..., description="Ảnh phiếu trắc nghiệm"),
    answer_key_file: Optional[UploadFile] = File(
        None, description="File Excel chứa đáp án"
    ),
):
    """
    Xử lý OMR và tạo ảnh kết quả có marking
    """
    try:
        # Tạo file tạm cho ảnh
        with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_image:
            content = await image_file.read()
            temp_image.write(content)
            temp_image_path = temp_image.name

        # Tạo file tạm cho Excel nếu có
        temp_excel_path = None
        if answer_key_file:
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=".xlsx"
            ) as temp_excel:
                excel_content = await answer_key_file.read()
                temp_excel.write(excel_content)
                temp_excel_path = temp_excel.name

        # Xử lý OMR
        result = omr_grading_service.process_omr_sheet(temp_image_path, temp_excel_path)

        # Tạo ảnh kết quả
        output_path = temp_image_path.replace(".jpg", "_result.jpg")
        omr_grading_service.create_result_visualization(
            temp_image_path, result, output_path
        )

        # Cleanup input files
        os.unlink(temp_image_path)
        if temp_excel_path:
            os.unlink(temp_excel_path)

        # Trả về ảnh kết quả
        return FileResponse(
            output_path,
            media_type="image/jpeg",
            filename="omr_result.jpg",
            background=lambda: os.unlink(output_path),  # Cleanup after response
        )

    except Exception as e:
        logger.error(f"Error processing OMR with visualization: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý OMR: {str(e)}")


@router.get("/test-sample-visualization")
async def test_sample_visualization():
    """
    Test tạo ảnh kết quả với dữ liệu mẫu
    """
    try:
        image_path = "data/grading/1.jpeg"
        excel_path = "data/grading/sample_answer_keys.xlsx"

        if not Path(image_path).exists():
            raise HTTPException(
                status_code=404, detail=f"Không tìm thấy file ảnh: {image_path}"
            )

        # Xử lý OMR
        result = omr_grading_service.process_omr_sheet(image_path, excel_path)

        # Tạo ảnh kết quả
        output_path = "data/grading/1_result.jpg"
        omr_grading_service.create_result_visualization(image_path, result, output_path)

        return FileResponse(
            output_path, media_type="image/jpeg", filename="sample_omr_result.jpg"
        )

    except Exception as e:
        logger.error(f"Error creating sample visualization: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi tạo ảnh kết quả: {str(e)}")


@router.get("/health")
async def health_check():
    """Kiểm tra trạng thái service OMR"""
    return {"status": "healthy", "service": "OMR Grading Service", "version": "1.0.0"}
